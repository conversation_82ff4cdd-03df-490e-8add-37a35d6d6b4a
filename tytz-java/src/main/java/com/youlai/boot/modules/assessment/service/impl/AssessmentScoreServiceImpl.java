package com.youlai.boot.modules.assessment.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.assessment.converter.AssessmentScoreConverter;
import com.youlai.boot.modules.assessment.mapper.AssessmentScoreMapper;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScore;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO;
import com.youlai.boot.modules.assessment.service.AssessmentScoreService;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentRuleTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 履职考核得分总表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@RequiredArgsConstructor
public class AssessmentScoreServiceImpl extends ServiceImpl<AssessmentScoreMapper, AssessmentScore>
        implements AssessmentScoreService {

    private final AssessmentScoreConverter assessmentScoreConverter;

    /**
     * 履职考核得分总表分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    @Override
    public IPage<AssessmentScoreVO> getAssessmentScorePage(AssessmentScoreQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<AssessmentScoreVO> page = new Page<>(pageNum, pageSize);

        // 如果没有传递年份，默认查询当年的数据
        if (queryParams.getYear() == null) {
            queryParams.setYear(LocalDateTime.now().getYear());
        }

        // 查询数据
        // Page<AssessmentScoreVO> result = this.baseMapper.getAssessmentScorePage(page,
        // queryParams);
        Page<AssessmentScoreVO> result = this.baseMapper.getAssessmentScorePageV2(page, queryParams);
        // 翻译所属商会
        for (AssessmentScoreVO record : result.getRecords()) {
            record.setCategoryLabel(record.getCategory().getLabel());
        }
        return result;
    }

    /**
     * 获取履职考核得分总表表单数据
     *
     * @param id 得分ID
     * @return 表单数据
     */
    @Override
    public AssessmentScoreForm getAssessmentScoreFormData(Long id) {
        AssessmentScore entity = this.getById(id);
        return assessmentScoreConverter.toForm(entity);
    }

    /**
     * 新增履职考核得分总表
     *
     * @param formData 得分表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveAssessmentScore(AssessmentScoreForm formData) {
        // 表单数据转换为实体
        AssessmentScore entity = assessmentScoreConverter.toEntity(formData);

        // 设置创建人
        entity.setCreateBy(SecurityUtils.getUserId());

        // 保存实体
        return this.save(entity);
    }

    /**
     * 修改履职考核得分总表
     *
     * @param id       得分ID
     * @param formData 得分表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateAssessmentScore(Long id, AssessmentScoreForm formData) {
        // 获取原实体
        AssessmentScore existingEntity = this.getById(id);
        Assert.notNull(existingEntity, "履职考核得分总表不存在");

        // 表单数据转换为实体
        AssessmentScore entity = assessmentScoreConverter.toEntity(formData);
        entity.setId(id);

        // 设置更新人
        entity.setUpdateBy(SecurityUtils.getUserId());

        // 更新实体
        return this.updateById(entity);
    }

    /**
     * 删除履职考核得分总表
     *
     * @param ids 得分ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteAssessmentScore(String ids) {
        Assert.notBlank(ids, "删除的履职考核得分总表ID不能为空");

        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());

        return this.removeByIds(idList);
    }

    /**
     * 更新用户的某个类型得分
     *
     * @param category   得分归类
     * @param memberId   用户ID
     * @param year       年度
     * @param type       得分类型
     * @param scoreDelta 得分变化量（可为负数）
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateMemberScore(AssessmentCategoryEnum category, Long memberId, Integer year,
            AssessmentRuleTypeEnum type, Integer scoreDelta) {
        // 查找或创建得分记录
        AssessmentScore scoreRecord = getOrCreateScoreRecord(category, memberId, year);

        // 根据类型更新对应的得分
        AssessmentRuleTypeEnum primaryType = type.getPrimaryType();
        switch (primaryType) {
            case MEETING:
                scoreRecord.setMeetingScore(scoreRecord.getMeetingScore() + scoreDelta);
                break;
            case ACTIVITY:
                scoreRecord.setActivityScore(scoreRecord.getActivityScore() + scoreDelta);
                break;
            case KEY_WORK:
                scoreRecord.setKeyWorkScore(scoreRecord.getKeyWorkScore() + scoreDelta);
                break;
            case ENVIRONMENT:
                scoreRecord.setEnvironmentScore(scoreRecord.getEnvironmentScore() + scoreDelta);
                break;
            case OPINION:
                scoreRecord.setOpinionScore(scoreRecord.getOpinionScore() + scoreDelta);
                break;
            default:
                // 其他类型暂不处理
                break;
        }

        // 重新计算总得分
        scoreRecord.setScore(scoreRecord.getActivityScore() + scoreRecord.getMeetingScore() +
                scoreRecord.getKeyWorkScore() + scoreRecord.getEnvironmentScore() +
                scoreRecord.getOpinionScore());

        // 设置更新人和更新时间
        scoreRecord.setUpdateBy(SecurityUtils.getUserId());
        scoreRecord.setUpdateTime(LocalDateTime.now());

        return this.updateById(scoreRecord);
    }

    /**
     * 重新计算用户的总得分
     *
     * @param category 得分归类
     * @param memberId 用户ID
     * @param year     年度
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean recalculateTotalScore(AssessmentCategoryEnum category, Long memberId, Integer year) {
        AssessmentScore scoreRecord = getOrCreateScoreRecord(category, memberId, year);

        // 重新计算总得分
        scoreRecord.setScore(scoreRecord.getActivityScore() + scoreRecord.getMeetingScore() +
                scoreRecord.getKeyWorkScore() + scoreRecord.getEnvironmentScore() +
                scoreRecord.getOpinionScore());

        // 设置更新人和更新时间
        scoreRecord.setUpdateBy(SecurityUtils.getUserId());
        scoreRecord.setUpdateTime(LocalDateTime.now());

        return this.updateById(scoreRecord);
    }

    /**
     * 获取或创建得分记录
     *
     * @param category 得分归类
     * @param memberId 用户ID
     * @param year     年度
     * @return 得分记录
     */
    private AssessmentScore getOrCreateScoreRecord(AssessmentCategoryEnum category, Long memberId, Integer year) {
        // 查找现有记录
        LambdaQueryWrapper<AssessmentScore> queryWrapper = new LambdaQueryWrapper<AssessmentScore>()
                .eq(AssessmentScore::getCategory, category)
                .eq(AssessmentScore::getMemberId, memberId)
                .eq(AssessmentScore::getYear, year);

        AssessmentScore scoreRecord = this.getOne(queryWrapper);

        // 如果不存在则创建新记录
        if (scoreRecord == null) {
            scoreRecord = new AssessmentScore();
            scoreRecord.setCategory(category);
            scoreRecord.setMemberId(memberId);
            scoreRecord.setYear(year);
            scoreRecord.setActivityScore(0);
            scoreRecord.setMeetingScore(0);
            scoreRecord.setKeyWorkScore(0);
            scoreRecord.setEnvironmentScore(0);
            scoreRecord.setOpinionScore(0);
            scoreRecord.setScore(0);
            scoreRecord.setCreateBy(SecurityUtils.getUserId());
            scoreRecord.setCreateTime(LocalDateTime.now());

            this.save(scoreRecord);
        }

        return scoreRecord;
    }
}
